import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/spare_part_category.dart';
import 'package:frontend/core/enums/spare_part_status.dart';
import 'package:frontend/core/enums/cylinder_type.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart';
import 'package:frontend/features/inventory/domain/entities/spare_part_entity.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_image_picker_card.dart';
import 'package:image_picker/image_picker.dart';

class CreateSparePartPage extends StatefulWidget {
  final bool isEditMode;
  final SparePartEntity? existingSparePart;

  const CreateSparePartPage({
    super.key,
    this.isEditMode = false,
    this.existingSparePart,
  });

  @override
  State<CreateSparePartPage> createState() => _CreateSparePartPageState();
}

class _CreateSparePartPageState extends State<CreateSparePartPage> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _costController = TextEditingController();
  final _quantityController = TextEditingController();
  final _minimumStockController = TextEditingController();
  final _barcodeController = TextEditingController();
  // final _imageUrlController = TextEditingController();

  SparePartCategory? _selectedCategory;
  SparePartStatus _selectedStatus = SparePartStatus.available;
  final List<CylinderType> _compatibleCylinderTypes = [];
  XFile? _selectedImageFile;

  @override
  void initState() {
    super.initState();
    if (widget.isEditMode && widget.existingSparePart != null) {
      _initializeEditMode();
    }
  }

  void _initializeEditMode() {
    final sparePart = widget.existingSparePart!;
    _descriptionController.text = sparePart.description;
    _priceController.text = sparePart.price.toString();
    _costController.text = sparePart.cost.toString();
    _quantityController.text = sparePart.quantity.toString();
    _minimumStockController.text = sparePart.minimumStockLevel.toString();
    _barcodeController.text = sparePart.barcode;

    _selectedCategory = sparePart.category;
    _compatibleCylinderTypes.clear();
    _compatibleCylinderTypes.addAll(sparePart.compatibleCylinderTypes);
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _priceController.dispose();
    _costController.dispose();
    _quantityController.dispose();
    _minimumStockController.dispose();
    _barcodeController.dispose();
    // _imageUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
        title: Text(
          'Create New Spare Part',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _resetForm,
            child: Text(
              'Reset',
              style: TextStyle(
                color: context.appColors.subtextColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: BlocListener<InventoryBloc, InventoryState>(
        listener: (context, state) {
          if (state is CreateSparePartSuccess) {
            SnackBarHelper.showSuccessSnackBar(
              context,
              message: 'Spare part created successfully!',
            );
            Navigator.of(context).pop();
          }
          if (state is CreateSparePartFailure) {
            SnackBarHelper.showErrorSnackBar(
              context,
              message:
                  'Failed to create spare part: ${state.appFailure.getErrorMessage()}',
            );
          }
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Basic Information'),
                SizedBox(height: 16.h),
                _buildDescriptionField(),
                SizedBox(height: 16.h),
                _buildCategoryAndStatusRow(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Product Image'),
                SizedBox(height: 16.h),
                _buildImagePickerSection(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Pricing'),
                SizedBox(height: 16.h),
                _buildPricingRow(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Stock Management'),
                SizedBox(height: 16.h),
                _buildStockRow(),
                SizedBox(height: 24.h),
                _buildSectionHeader('Compatibility'),
                SizedBox(height: 16.h),
                _buildCompatibilitySection(),
                SizedBox(height: 32.h),
                _buildCreateButton(),
                SizedBox(height: 16.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        color: context.appColors.textColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      maxLines: 3,
      decoration: InputDecoration(
        labelText: 'Description',
        hintText: 'Enter spare part description',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter a description';
        }
        return null;
      },
    );
  }

  Widget _buildCategoryAndStatusRow() {
    return Column(
      children: [
        _buildDropdownField<SparePartCategory>(
          label: 'Category',
          value: _selectedCategory,
          items: SparePartCategory.values,
          onChanged: (value) => setState(() => _selectedCategory = value),
          itemBuilder: (category) => category.displayName,
          validator: (value) =>
              value == null ? 'Please select a category' : null,
        ),
        SizedBox(height: 16.h),
        _buildDropdownField<SparePartStatus>(
          label: 'Status',
          value: _selectedStatus,
          items: SparePartStatus.values,
          onChanged: (value) => setState(() => _selectedStatus = value!),
          itemBuilder: (status) => status.displayName,
        ),
      ],
    );
  }

  Widget _buildImagePickerSection() {
    return Center(
      child: Column(
        children: [
          CustomImagePickerCard(
            imageFile: _selectedImageFile,
            imageUrl:
                widget.isEditMode &&
                    widget.existingSparePart != null &&
                    widget.existingSparePart!.imageUrl.isNotEmpty
                ? widget.existingSparePart!.formattedImageUrl
                : null,
            radius: 60.0,
            showIcon: true,
            isLoading: false,
            onCamera: (XFile pickedImage) {
              setState(() {
                _selectedImageFile = pickedImage;
              });
            },
            onGallery: (XFile pickedImage) {
              setState(() {
                _selectedImageFile = pickedImage;
              });
            },
          ),
          SizedBox(height: 12.h),
          Text(
            'Tap to add spare part image',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          if (_selectedImageFile != null) ...[
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle,
                  color: context.appColors.successColor,
                  size: 16.w,
                ),
                SizedBox(width: 4.w),
                Text(
                  'Image selected',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: context.appColors.successColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 8.w),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedImageFile = null;
                    });
                  },
                  child: Icon(
                    Icons.close,
                    color: context.appColors.errorColor,
                    size: 16.w,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPricingRow() {
    return Row(
      children: [
        Expanded(
          child: _buildNumberField(
            controller: _priceController,
            label: 'Selling Price',
            hint: '0.00',
            prefix: '\$',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final price = double.tryParse(value);
              if (price == null || price <= 0) return 'Invalid price';
              final cost = double.tryParse(_costController.text);
              if (cost != null && price < cost) return 'Price must be ≥ cost';
              return null;
            },
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildNumberField(
            controller: _costController,
            label: 'Cost Price',
            hint: '0.00',
            prefix: '\$',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final cost = double.tryParse(value);
              if (cost == null || cost < 0) return 'Invalid cost';

              final price = double.tryParse(_priceController.text);
              if (price != null && price < cost) return 'Price must be ≥ cost';
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStockRow() {
    return Row(
      children: [
        Expanded(
          child: _buildNumberField(
            controller: _quantityController,
            label: 'Initial Quantity',
            hint: '0',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final quantity = int.tryParse(value);
              if (quantity == null || quantity < 0) return 'Invalid quantity';
              return null;
            },
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: _buildNumberField(
            controller: _minimumStockController,
            label: 'Minimum Stock Level',
            hint: '10',
            validator: (value) {
              if (value == null || value.isEmpty) return 'Required';
              final minStock = int.tryParse(value);
              if (minStock == null || minStock < 0) return 'Invalid value';
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCompatibilitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Compatible Cylinder Types (${_compatibleCylinderTypes.length})',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _showAddCompatibilityBottomSheet,
                icon: const Icon(Icons.add),
                label: const Text('Add Compatible Types'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.appColors.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        if (_compatibleCylinderTypes.isEmpty)
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: context.appColors.dividerColor),
            ),
            child: Center(
              child: Text(
                'No compatible cylinder types added yet',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
            ),
          )
        else
          Wrap(
            spacing: 8.w,
            runSpacing: 8.h,
            children: _compatibleCylinderTypes.map((type) {
              return Chip(
                label: Text(type.displayName),
                deleteIcon: Icon(
                  Icons.close,
                  size: 16.sp,
                  color: context.appColors.errorColor,
                ),
                onDeleted: () => _removeCompatibleType(type),
                backgroundColor: context.appColors.primaryColor.withValues(
                  alpha: 0.1,
                ),
                side: BorderSide(color: context.appColors.primaryColor),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildDropdownField<T>({
    required String label,
    required T? value,
    required List<T> items,
    required ValueChanged<T?> onChanged,
    required String Function(T) itemBuilder,
    String? Function(T?)? validator,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      items: items.map((item) {
        return DropdownMenuItem<T>(value: item, child: Text(itemBuilder(item)));
      }).toList(),
      onChanged: onChanged,
      validator: validator,
    );
  }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? prefix,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixText: prefix,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.dividerColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8.r),
          borderSide: BorderSide(color: context.appColors.primaryColor),
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildCreateButton() {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        final isLoading = state is CreateSparePartLoading;

        return SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed: isLoading ? null : _createSparePart,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    'Create Spare Part',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        );
      },
    );
  }

  void _showAddCompatibilityBottomSheet() {
    final availableTypes = CylinderType.values
        .where((type) => !_compatibleCylinderTypes.contains(type))
        .toList();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (BuildContext bottomSheetContext) {
        // Local state for multi-selection
        List<CylinderType> selectedTypes = [];
        bool selectAll = false;

        return StatefulBuilder(
          builder: (context, setBottomSheetState) {
            return Container(
              padding: EdgeInsets.fromLTRB(
                20.w,
                20.h,
                20.w,
                MediaQuery.of(context).viewInsets.bottom + 20.h,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Handle bar
                  Center(
                    child: Container(
                      width: 40.w,
                      height: 4.h,
                      decoration: BoxDecoration(
                        color: context.appColors.dividerColor,
                        borderRadius: BorderRadius.circular(2.r),
                      ),
                    ),
                  ),
                  SizedBox(height: 20.h),

                  // Title and Select All
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Select Compatible Cylinder Types',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(
                                    color: context.appColors.textColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              'Choose which cylinder types are compatible with this spare part',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(
                                    color: context.appColors.subtextColor,
                                  ),
                            ),
                          ],
                        ),
                      ),
                      if (availableTypes.isNotEmpty)
                        TextButton.icon(
                          onPressed: () {
                            setBottomSheetState(() {
                              selectAll = !selectAll;
                              if (selectAll) {
                                selectedTypes = List.from(availableTypes);
                              } else {
                                selectedTypes.clear();
                              }
                            });
                          },
                          icon: Icon(
                            selectAll ? Icons.deselect : Icons.select_all,
                            size: 18.sp,
                          ),
                          label: Text(
                            selectAll ? 'Deselect All' : 'Select All',
                            style: TextStyle(fontSize: 12.sp),
                          ),
                          style: TextButton.styleFrom(
                            foregroundColor: context.appColors.primaryColor,
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 20.h),

                  // Content
                  if (availableTypes.isEmpty)
                    Container(
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                        color: context.appColors.surfaceColor,
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                          color: context.appColors.dividerColor,
                        ),
                      ),
                      child: Center(
                        child: Column(
                          children: [
                            Icon(
                              Icons.check_circle_outline,
                              size: 48.sp,
                              color: context.appColors.successColor,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'All cylinder types have been added!',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(
                                    color: context.appColors.textColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    Column(
                      children: [
                        // Selection counter
                        if (selectedTypes.isNotEmpty)
                          Container(
                            margin: EdgeInsets.only(bottom: 12.h),
                            padding: EdgeInsets.symmetric(
                              horizontal: 12.w,
                              vertical: 8.h,
                            ),
                            decoration: BoxDecoration(
                              color: context.appColors.primaryColor.withValues(
                                alpha: 0.1,
                              ),
                              borderRadius: BorderRadius.circular(6.r),
                              border: Border.all(
                                color: context.appColors.primaryColor
                                    .withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.check_circle,
                                  color: context.appColors.primaryColor,
                                  size: 16.sp,
                                ),
                                SizedBox(width: 6.w),
                                Text(
                                  '${selectedTypes.length} selected',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: context.appColors.primaryColor,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              ],
                            ),
                          ),

                        // Cylinder type list with checkboxes
                        ...availableTypes.map((type) {
                          final isSelected = selectedTypes.contains(type);
                          return Container(
                            margin: EdgeInsets.only(bottom: 8.h),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () {
                                  setBottomSheetState(() {
                                    if (isSelected) {
                                      selectedTypes.remove(type);
                                      // Update selectAll state
                                      if (selectedTypes.length <
                                          availableTypes.length) {
                                        selectAll = false;
                                      }
                                    } else {
                                      selectedTypes.add(type);
                                      // Update selectAll state
                                      if (selectedTypes.length ==
                                          availableTypes.length) {
                                        selectAll = true;
                                      }
                                    }
                                  });
                                },
                                borderRadius: BorderRadius.circular(8.r),
                                child: Container(
                                  padding: EdgeInsets.all(16.w),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: isSelected
                                          ? context.appColors.primaryColor
                                          : context.appColors.dividerColor,
                                      width: isSelected ? 2 : 1,
                                    ),
                                    borderRadius: BorderRadius.circular(8.r),
                                    color: isSelected
                                        ? context.appColors.primaryColor
                                              .withValues(alpha: 0.05)
                                        : null,
                                  ),
                                  child: Row(
                                    children: [
                                      // Checkbox
                                      Container(
                                        width: 24.w,
                                        height: 24.h,
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? context.appColors.primaryColor
                                              : Colors.transparent,
                                          border: Border.all(
                                            color: isSelected
                                                ? context.appColors.primaryColor
                                                : context
                                                      .appColors
                                                      .dividerColor,
                                            width: 2,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            4.r,
                                          ),
                                        ),
                                        child: isSelected
                                            ? Icon(
                                                Icons.check,
                                                color: Colors.white,
                                                size: 16.sp,
                                              )
                                            : null,
                                      ),
                                      SizedBox(width: 12.w),

                                      // Cylinder icon
                                      Container(
                                        width: 40.w,
                                        height: 40.h,
                                        decoration: BoxDecoration(
                                          color: context.appColors.primaryColor
                                              .withValues(
                                                alpha: isSelected ? 0.2 : 0.1,
                                              ),
                                          borderRadius: BorderRadius.circular(
                                            8.r,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.propane_tank,
                                          color: context.appColors.primaryColor,
                                          size: 20.sp,
                                        ),
                                      ),
                                      SizedBox(width: 12.w),

                                      // Cylinder info
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              type.displayName,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyLarge
                                                  ?.copyWith(
                                                    color: context
                                                        .appColors
                                                        .textColor,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                            ),
                                            Text(
                                              '${type.weight} kg cylinder',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodySmall
                                                  ?.copyWith(
                                                    color: context
                                                        .appColors
                                                        .subtextColor,
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        }),
                      ],
                    ),

                  SizedBox(height: 20.h),

                  // Action buttons
                  if (availableTypes.isNotEmpty)
                    Row(
                      children: [
                        // Cancel button
                        Expanded(
                          child: TextButton(
                            onPressed: () =>
                                Navigator.of(bottomSheetContext).pop(),
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(vertical: 12.h),
                              side: BorderSide(
                                color: context.appColors.dividerColor,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                            ),
                            child: Text(
                              'Cancel',
                              style: TextStyle(
                                color: context.appColors.subtextColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 12.w),

                        // Add selected button
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: selectedTypes.isEmpty
                                ? null
                                : () {
                                    // Add all selected types
                                    _addMultipleCompatibleTypes(selectedTypes);
                                    Navigator.of(bottomSheetContext).pop();
                                  },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: selectedTypes.isEmpty
                                  ? context.appColors.disabledColor
                                  : context.appColors.primaryColor,
                              foregroundColor: Colors.white,
                              padding: EdgeInsets.symmetric(vertical: 12.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                            ),
                            child: Text(
                              selectedTypes.isEmpty
                                  ? 'Select Types'
                                  : 'Add ${selectedTypes.length} Type${selectedTypes.length > 1 ? 's' : ''}',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                  else
                    // Close button when no types available
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () => Navigator.of(bottomSheetContext).pop(),
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                        ),
                        child: Text(
                          'Close',
                          style: TextStyle(
                            color: context.appColors.subtextColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _addCompatibleType(CylinderType type) {
    setState(() {
      if (!_compatibleCylinderTypes.contains(type)) {
        _compatibleCylinderTypes.add(type);
      }
    });
  }

  void _addMultipleCompatibleTypes(List<CylinderType> types) {
    setState(() {
      for (final type in types) {
        if (!_compatibleCylinderTypes.contains(type)) {
          _compatibleCylinderTypes.add(type);
        }
      }
    });
  }

  void _removeCompatibleType(CylinderType type) {
    setState(() {
      _compatibleCylinderTypes.remove(type);
    });
  }

  void _createSparePart() {
    if (_formKey.currentState!.validate()) {
      final event = CreateSparePartEvent(
        description: _descriptionController.text.trim(),
        category: _selectedCategory!,
        price: double.parse(_priceController.text),
        cost: double.parse(_costController.text),
        barcode: _barcodeController.text.trim(),
        minimumStockLevel: int.parse(_minimumStockController.text),
        compatibleCylinderTypes: _compatibleCylinderTypes,
        initialStock: int.parse(_quantityController.text),
        imageFile: _selectedImageFile,
      );

      context.inventoryBloc.add(event);
    }
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    _descriptionController.clear();
    _priceController.clear();
    _costController.clear();
    _quantityController.clear();
    _minimumStockController.clear();
    _barcodeController.clear();
    // _imageUrlController.clear();
    setState(() {
      _selectedCategory = null;
      _selectedStatus = SparePartStatus.available;
      _compatibleCylinderTypes.clear();
      _selectedImageFile = null;
    });

    // unfocus
    FocusScope.of(context).unfocus();
  }
}
