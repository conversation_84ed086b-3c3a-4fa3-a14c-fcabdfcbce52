"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.packageService = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const package_model_1 = require("../models/package.model");
const cylinder_model_1 = require("../models/cylinder.model");
const spareParts_model_1 = require("../models/spareParts.model");
const app_errors_1 = require("../errors/app_errors");
const logger_1 = __importDefault(require("../config/logger"));
const file_cleanup_1 = require("../utils/file_cleanup");
class PackageService {
    /**
     * Create a new package
     * @throws {DuplicateResourceError} If package with same name exists
     * @throws {NotFoundError} If cylinder or spare parts not found
     * @throws {BadRequestError} If validation fails
     */
    async createPackage(data, session) {
        const sessionToUse = session || (await mongoose_1.default.startSession());
        const shouldCommit = !session;
        try {
            if (!session)
                sessionToUse.startTransaction();
            // Check for duplicate package name
            const existingPackage = await package_model_1.Package.findOne({ name: data.name }).session(sessionToUse);
            if (existingPackage) {
                throw new app_errors_1.DuplicateResourceError('Package with this name already exists', {
                    code: 'DUPLICATE_PACKAGE',
                });
            }
            // Validate cylinder exists
            const cylinderExists = await cylinder_model_1.Cylinder.exists({ _id: data.cylinder }).session(sessionToUse);
            if (!cylinderExists) {
                throw new app_errors_1.NotFoundError('Cylinder not found', {
                    code: 'CYLINDER_NOT_FOUND',
                });
            }
            // Validate spare parts exist and are active
            await this.validateSpareParts(data.includedSpareParts, sessionToUse);
            // Calculate prices only if not provided
            const costPrice = data.costPrice ??
                (await this.calculatePackageCost(data.cylinder, data.includedSpareParts, sessionToUse));
            const totalPrice = data.totalPrice ??
                (await this.calculatePackagePrice(data.cylinder, data.includedSpareParts, data.discount || 0, sessionToUse));
            const newPackage = new package_model_1.Package({
                isActive: true,
                quantity: 0,
                reserved: 0,
                sold: 0,
                minimumStockLevel: 10,
                ...data,
                costPrice, // Will use either provided or calculated
                totalPrice, // Will use either provided or calculated
            });
            await newPackage.save({ session: sessionToUse });
            if (shouldCommit) {
                await sessionToUse.commitTransaction();
            }
            return newPackage;
        }
        catch (error) {
            if (shouldCommit) {
                try {
                    await sessionToUse.abortTransaction();
                }
                catch (error) {
                    logger_1.default.error('Failed to abort transaction', {
                        error: error.message,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            throw error;
        }
        finally {
            if (shouldCommit) {
                sessionToUse.endSession();
            }
        }
    }
    /**
     * Update package details
     * @throws {NotFoundError} If package or related items not found
     */
    async updatePackage(id, updateData, session) {
        const sessionToUse = session || (await mongoose_1.default.startSession());
        const shouldCommit = !session;
        try {
            if (!session)
                sessionToUse.startTransaction();
            // Get current package first
            const currentPackage = await package_model_1.Package.findById(id).session(sessionToUse);
            if (!currentPackage) {
                throw new app_errors_1.NotFoundError('Package not found', { code: 'PACKAGE_NOT_FOUND' });
            }
            // Prevent certain fields from being updated directly
            const { totalPrice, costPrice, quantity, reserved, sold, availableQuantity, ...safeUpdateData } = updateData;
            // Create update object
            const finalUpdateData = { ...safeUpdateData };
            // Handle price calculations based on different scenarios
            const shouldRecalculatePrices = 
            // If prices weren't provided in update AND aren't set in current doc
            (totalPrice === undefined && !currentPackage.totalPrice) ||
                (costPrice === undefined && !currentPackage.costPrice) ||
                // OR if any price-affecting fields are being updated
                updateData.cylinder ||
                updateData.includedSpareParts ||
                updateData.discount !== undefined;
            if (shouldRecalculatePrices) {
                // Use updated cylinder ID if provided, otherwise use current one
                const cylinderId = updateData.cylinder ? updateData.cylinder : currentPackage.cylinder;
                // Use updated spare parts if provided, otherwise use current ones
                const spareParts = updateData.includedSpareParts
                    ? updateData.includedSpareParts
                    : currentPackage.includedSpareParts;
                // Use updated discount if provided, otherwise use current one
                const discount = updateData.discount !== undefined ? updateData.discount : currentPackage.discount || 0;
                // Only calculate prices that need to be calculated
                if (costPrice === undefined && !currentPackage.costPrice) {
                    finalUpdateData.costPrice = await this.calculatePackageCost(new mongoose_1.Types.ObjectId(cylinderId.toString()), spareParts, sessionToUse);
                }
                if (totalPrice === undefined && !currentPackage.totalPrice) {
                    finalUpdateData.totalPrice = await this.calculatePackagePrice(new mongoose_1.Types.ObjectId(cylinderId.toString()), spareParts, discount, sessionToUse);
                }
            }
            // If prices were explicitly provided in update, use them
            if (totalPrice !== undefined) {
                finalUpdateData.totalPrice = totalPrice;
            }
            if (costPrice !== undefined) {
                finalUpdateData.costPrice = costPrice;
            }
            const updatedPackage = await package_model_1.Package.findByIdAndUpdate(id, finalUpdateData, {
                new: true,
                runValidators: true,
                session: sessionToUse,
            });
            if (!updatedPackage) {
                throw new app_errors_1.NotFoundError('Package not found', {
                    code: 'PACKAGE_NOT_FOUND',
                });
            }
            if (shouldCommit) {
                await sessionToUse.commitTransaction();
            }
            return updatedPackage;
        }
        catch (error) {
            if (shouldCommit) {
                try {
                    await sessionToUse.abortTransaction();
                }
                catch (error) {
                    logger_1.default.error('Failed to abort transaction', {
                        error: error.message,
                        timestamp: new Date().toISOString(),
                    });
                }
            }
            throw error;
        }
        finally {
            if (shouldCommit) {
                sessionToUse.endSession();
            }
        }
    }
    /**
     * Get package by ID with populated data (excludes soft-deleted items)
     * @throws {NotFoundError} If package not found
     */
    async getPackageById(id, populate = true, includeDeleted = false) {
        const queryFilter = { _id: id };
        if (!includeDeleted) {
            queryFilter.isDeleted = false;
        }
        let query = package_model_1.Package.findOne(queryFilter);
        if (populate) {
            query = query.populate('cylinder').populate('includedSpareParts.part');
        }
        const packageDoc = await query;
        if (!packageDoc) {
            throw new app_errors_1.NotFoundError('Package not found', {
                code: 'PACKAGE_NOT_FOUND',
            });
        }
        return packageDoc;
    }
    /**
     * List packages with filtering and pagination
     */
    async listPackages(filters = {}, pagination = { page: 1, limit: 10 }, populate = true) {
        const query = {};
        // Exclude soft-deleted items by default
        if (!filters.includeDeleted) {
            query.isDeleted = false;
        }
        // Text search
        if (filters.search) {
            query.$text = { $search: filters.search };
        }
        // Cylinder filter
        if (filters.cylinder) {
            query.cylinder = filters.cylinder;
        }
        // Active filter
        if (filters.isActive !== undefined) {
            query.isActive = filters.isActive;
        }
        // Price range filter
        if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
            query.totalPrice = {};
            if (filters.minPrice !== undefined)
                query.totalPrice.$gte = filters.minPrice;
            if (filters.maxPrice !== undefined)
                query.totalPrice.$lte = filters.maxPrice;
        }
        let findQuery = package_model_1.Package.find(query)
            .sort({ createdAt: -1 })
            .skip((pagination.page - 1) * pagination.limit)
            .limit(pagination.limit);
        if (populate) {
            findQuery = findQuery.populate('cylinder').populate('includedSpareParts.part');
        }
        const [data, total] = await Promise.all([findQuery.exec(), package_model_1.Package.countDocuments(query)]);
        return { data, total };
    }
    /**
     * Toggle package active status
     * @throws {NotFoundError} If package not found
     */
    async togglePackageStatus(id, session) {
        const packageDoc = await package_model_1.Package.findById(id).session(session || null);
        if (!packageDoc) {
            throw new app_errors_1.NotFoundError('Package not found', {
                code: 'PACKAGE_NOT_FOUND',
            });
        }
        const updatedPackage = await package_model_1.Package.findByIdAndUpdate(id, { isActive: !packageDoc.isActive }, { new: true, session });
        return updatedPackage;
    }
    /**
     * Validate all spare parts in a package
     * @throws {NotFoundError} If any spare part not found
     * @throws {BadRequestError} If any spare part is inactive
     */
    async validateSpareParts(items, session) {
        if (!items || items.length === 0) {
            throw new app_errors_1.BadRequestError('Package must include at least one spare part', {
                code: 'EMPTY_PACKAGE',
            });
        }
        const partIds = items.map(item => item.part);
        const spareParts = await spareParts_model_1.SparePart.find({
            _id: { $in: partIds },
        }).session(session || null);
        // Check all parts exist
        if (spareParts.length !== items.length) {
            const foundIds = spareParts.map(p => p._id.toString());
            const missingIds = partIds.filter(id => !foundIds.includes(id.toString()));
            throw new app_errors_1.NotFoundError(`Spare parts not found: ${missingIds.join(', ')}`, {
                code: 'SPARE_PARTS_NOT_FOUND',
            });
        }
        // Check all parts are active
        const inactiveParts = spareParts.filter(p => p.status !== 'AVAILABLE');
        if (inactiveParts.length > 0) {
            throw new app_errors_1.BadRequestError(`Cannot include inactive spare parts: ${inactiveParts.map(p => p.category).join(', ')}`, { code: 'INACTIVE_SPARE_PARTS' });
        }
    }
    /**
     * Calculate package cost based on cylinder and included spare parts
     */
    async calculatePackageCost(cylinderId, items, session) {
        // Get cylinder cost
        const cylinder = await cylinder_model_1.Cylinder.findById(cylinderId).session(session || null);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found for cost calculation', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        // Get spare parts costs
        const partIds = items.map(item => item.part);
        const spareParts = await spareParts_model_1.SparePart.find({
            _id: { $in: partIds },
        }).session(session || null);
        // Calculate total cost: cylinder cost + spare parts costs
        const sparePartsCost = items.reduce((total, item) => {
            const part = spareParts.find(p => p._id.toString() === item.part.toString());
            if (!part)
                return total;
            return total + part.cost * item.quantity;
        }, 0);
        return cylinder.cost + sparePartsCost;
    }
    /**
     * Calculate package price based on cylinder and spare parts prices with discount
     */
    async calculatePackagePrice(cylinderId, items, discount = 0, session) {
        if (discount < 0 || discount > 100) {
            throw new app_errors_1.BadRequestError('Discount must be between 0 and 100', {
                code: 'INVALID_DISCOUNT',
            });
        }
        // Get cylinder price
        const cylinder = await cylinder_model_1.Cylinder.findById(cylinderId).session(session || null);
        if (!cylinder) {
            throw new app_errors_1.NotFoundError('Cylinder not found for price calculation', {
                code: 'CYLINDER_NOT_FOUND',
            });
        }
        // Get spare parts prices
        const partIds = items.map(item => item.part);
        const spareParts = await spareParts_model_1.SparePart.find({
            _id: { $in: partIds },
        }).session(session || null);
        // Calculate total price: cylinder price + spare parts prices
        const sparePartsPrice = items.reduce((total, item) => {
            const part = spareParts.find(p => p._id.toString() === item.part.toString());
            if (!part)
                return total;
            return total + part.price * item.quantity;
        }, 0);
        const totalPriceBeforeDiscount = cylinder.price + sparePartsPrice;
        return totalPriceBeforeDiscount * (1 - discount / 100);
    }
    /**
     * Get package analytics
     */
    async getPackageAnalytics() {
        const results = await Promise.all([
            package_model_1.Package.countDocuments(),
            package_model_1.Package.countDocuments({ isActive: true }),
            package_model_1.Package.aggregate([{ $group: { _id: null, avgPrice: { $avg: '$totalPrice' } } }]),
            package_model_1.Package.aggregate([
                { $unwind: '$includedSpareParts' },
                { $group: { _id: '$includedSpareParts.part', count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 1 },
            ]),
        ]);
        return {
            totalPackages: results[0],
            activePackages: results[1],
            averagePrice: results[2][0]?.avgPrice || 0,
            mostIncludedPart: {
                part: results[3][0]?._id || null,
                count: results[3][0]?.count || 0,
            },
        };
    }
    // ==================== INVENTORY MANAGEMENT METHODS ====================
    /**
     * Reserve packages for an order
     * @param packageId - The package ID to reserve
     * @param quantity - Number of packages to reserve
     * @param orderId - The order ID for tracking
     * @param session - Optional MongoDB session for transactions
     * @throws {BadRequestError} If quantity is invalid or insufficient stock
     * @throws {NotFoundError} If package not found
     */
    async reservePackage(packageId, quantity, orderId, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const packageDoc = await package_model_1.Package.findById(packageId).session(session || null);
        if (!packageDoc) {
            throw new app_errors_1.NotFoundError('Package not found', {
                code: 'PACKAGE_NOT_FOUND',
            });
        }
        if (!packageDoc.isActive) {
            throw new app_errors_1.BadRequestError(`Cannot reserve inactive package`, {
                code: 'INACTIVE_PACKAGE',
            });
        }
        if (packageDoc.availableQuantity < quantity) {
            throw new app_errors_1.BadRequestError('Insufficient package stock', {
                code: 'INSUFFICIENT_PACKAGE_STOCK',
                details: {
                    available: packageDoc.availableQuantity,
                    requested: quantity,
                },
            });
        }
        const update = {
            $inc: { reserved: quantity },
        };
        // Update status if this reservation brings us to out of stock
        let newStatus;
        if (packageDoc.quantity - (packageDoc.reserved + quantity) <= 0) {
            // update.$set = { isActive: false };
            newStatus = false;
        }
        const result = await package_model_1.Package.updateOne({
            _id: packageId,
            $expr: { $gte: [{ $subtract: ['$quantity', '$reserved'] }, quantity] },
        }, update, { session });
        if (result.modifiedCount === 0) {
            throw new app_errors_1.DuplicateResourceError('Package stock changed during reservation', {
                code: 'CONCURRENT_MODIFICATION',
            });
        }
        logger_1.default.info('Package reserved successfully', {
            packageId,
            quantity,
            orderId,
            timestamp: new Date().toISOString(),
        });
        return {
            modifiedCount: result.modifiedCount,
            newStatus,
        };
    }
    /**
     * Release reserved packages (when order is cancelled)
     * @param packageId - The package ID to release reservation for
     * @param quantity - Number of packages to release
     * @param orderId - The order ID for tracking
     * @param session - Optional MongoDB session for transactions
     * @throws {BadRequestError} If quantity is invalid or exceeds reserved amount
     * @throws {NotFoundError} If package not found
     */
    async releaseReservation(packageId, quantity, orderId, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const packageDoc = await package_model_1.Package.findById(packageId).session(session || null);
        if (!packageDoc) {
            throw new app_errors_1.NotFoundError('Package not found', {
                code: 'PACKAGE_NOT_FOUND',
            });
        }
        if (packageDoc.reserved < quantity) {
            throw new app_errors_1.BadRequestError('Cannot release more than reserved quantity', {
                code: 'INVALID_RESERVATION_RELEASE',
                details: {
                    reserved: packageDoc.reserved,
                    toRelease: quantity,
                },
            });
        }
        const update = {
            $inc: { reserved: -quantity },
        };
        // Update status if we're coming back from out of stock
        let newStatus;
        if (!packageDoc.isActive && packageDoc.quantity - (packageDoc.reserved - quantity) > 0) {
            update.$set = { isActive: true };
            newStatus = true;
        }
        const result = await package_model_1.Package.updateOne({ _id: packageId, reserved: { $gte: quantity } }, update, { session });
        if (result.modifiedCount === 0) {
            throw new app_errors_1.DuplicateResourceError('Package stock changed during release', {
                code: 'CONCURRENT_MODIFICATION',
            });
        }
        logger_1.default.info('Package reservation released successfully', {
            packageId,
            quantity,
            orderId,
            timestamp: new Date().toISOString(),
        });
        return {
            modifiedCount: result.modifiedCount,
            newStatus,
        };
    }
    /**
     * Mark reserved packages as sold (when order is completed)
     * @param packageId - The package ID to mark as sold
     * @param quantity - Number of packages sold
     * @param session - Optional MongoDB session for transactions
     * @throws {BadRequestError} If quantity is invalid
     * @throws {NotFoundError} If package not found
     */
    async markPackageAsSold(packageId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const packageDoc = await package_model_1.Package.findByIdAndUpdate(packageId, {
            $inc: {
                reserved: -quantity,
                sold: quantity,
                quantity: -quantity, // Physical count decreases when sold
            },
        }, { new: true, session });
        if (!packageDoc) {
            throw new app_errors_1.NotFoundError('Package not found', {
                code: 'PACKAGE_NOT_FOUND',
            });
        }
        logger_1.default.info('Package marked as sold successfully', {
            packageId,
            quantity,
            remainingQuantity: packageDoc.quantity,
            timestamp: new Date().toISOString(),
        });
        return packageDoc;
    }
    /**
     * Get sales statistics aggregated by package
     * @returns Sales statistics including total sold and breakdown by package
     */
    async getPackageSalesStatistics() {
        const packages = await package_model_1.Package.find({ sold: { $gt: 0 } });
        let totalSold = 0;
        let totalRevenue = 0;
        const byPackage = packages.map(pkg => {
            totalSold += pkg.sold;
            const revenue = pkg.sold * pkg.totalPrice;
            totalRevenue += revenue;
            return {
                packageId: pkg._id.toString(),
                name: pkg.name,
                sold: pkg.sold,
                revenue,
            };
        });
        return {
            totalSold,
            byPackage: byPackage.sort((a, b) => b.sold - a.sold), // Sort by most sold
            totalRevenue,
        };
    }
    /**
     * Restock packages and update status if needed
     * @param packageId - The package ID to restock
     * @param quantity - Number of packages to add to stock
     * @param session - Optional MongoDB session for transactions
     * @throws {BadRequestError} If quantity is invalid
     * @throws {NotFoundError} If package not found
     */
    async restockPackage(packageId, quantity, session) {
        if (quantity <= 0) {
            throw new app_errors_1.BadRequestError('Quantity must be positive', {
                code: 'INVALID_QUANTITY',
            });
        }
        const update = {
            $inc: { quantity },
            lastRestockedAt: new Date(),
        };
        // Update status if coming from inactive
        update.$set = {
            isActive: true,
            // status : PackageStatus.Active,
        };
        const packageDoc = await package_model_1.Package.findByIdAndUpdate(packageId, update, {
            new: true,
            session,
        });
        if (!packageDoc) {
            throw new app_errors_1.NotFoundError('Package not found', {
                code: 'PACKAGE_NOT_FOUND',
            });
        }
        logger_1.default.info('Package restocked successfully', {
            packageId,
            quantity,
            newQuantity: packageDoc.quantity,
            timestamp: new Date().toISOString(),
        });
        return packageDoc;
    }
    /**
     * Adjust package stock (for damage, loss, etc.)
     * @param packageId - The package ID to adjust stock for
     * @param adjustment - Stock adjustment (positive or negative)
     * @param reason - Reason for adjustment
     * @param session - Optional MongoDB session for transactions
     * @throws {BadRequestError} If adjustment would result in negative stock
     * @throws {NotFoundError} If package not found
     */
    async adjustPackageStock(packageId, adjustment, reason, session) {
        if (adjustment === 0) {
            throw new app_errors_1.BadRequestError('Adjustment cannot be zero', {
                code: 'INVALID_ADJUSTMENT',
            });
        }
        const packageDoc = await package_model_1.Package.findById(packageId).session(session || null);
        if (!packageDoc) {
            throw new app_errors_1.NotFoundError('Package not found', {
                code: 'PACKAGE_NOT_FOUND',
            });
        }
        if (packageDoc.quantity + adjustment < 0) {
            throw new app_errors_1.BadRequestError('Adjustment would result in negative stock', {
                code: 'NEGATIVE_STOCK_ADJUSTMENT',
                details: {
                    currentQuantity: packageDoc.quantity,
                    adjustment,
                    resultingQuantity: packageDoc.quantity + adjustment,
                },
            });
        }
        const update = {
            $inc: { quantity: adjustment },
        };
        // Update status based on resulting quantity
        if (packageDoc.quantity + adjustment <= 0) {
            update.$set = { isActive: false };
        }
        else if (!packageDoc.isActive && adjustment > 0) {
            update.$set = { isActive: true };
        }
        const updatedPackage = await package_model_1.Package.findByIdAndUpdate(packageId, update, {
            new: true,
            session,
        });
        logger_1.default.info('Package stock adjusted', {
            packageId,
            adjustment,
            reason,
            newQuantity: updatedPackage.quantity,
            timestamp: new Date().toISOString(),
        });
        return updatedPackage;
    }
    /**
     * Get packages with low stock (quantity <= minimumStockLevel)
     * @returns Array of packages that need restocking
     */
    async getLowStockPackages() {
        return package_model_1.Package.find({
            $expr: { $lte: ['$quantity', '$minimumStockLevel'] },
            isActive: true, // Only active packages
        }).sort({ quantity: 1 }); // Sort by most critical first
    }
    /**
     * Check package availability
     * @param packageId - The package ID to check
     * @param requestedQuantity - Quantity requested
     * @throws {NotFoundError} If package not found
     * @returns Availability information
     */
    async checkPackageAvailability(packageId, requestedQuantity) {
        const packageDoc = await package_model_1.Package.findById(packageId);
        if (!packageDoc) {
            throw new app_errors_1.NotFoundError('Package not found', {
                code: 'PACKAGE_NOT_FOUND',
            });
        }
        return {
            available: packageDoc.isActive && packageDoc.availableQuantity >= requestedQuantity,
            availableQuantity: packageDoc.availableQuantity,
            isActive: packageDoc.isActive,
        };
    }
    /**
     * Get available package quantity (total - reserved)
     * @param packageId - The package ID to check
     * @throws {NotFoundError} If package not found
     * @returns Available quantity
     */
    async getAvailablePackageQuantity(packageId) {
        const packageDoc = await package_model_1.Package.findById(packageId);
        if (!packageDoc) {
            throw new app_errors_1.NotFoundError('Package not found', {
                code: 'PACKAGE_NOT_FOUND',
            });
        }
        return packageDoc.availableQuantity;
    }
    /**
     * Bulk update package statuses
     * @param packageIds - Array of package IDs to update
     * @param isActive - New active status
     * @param session - Optional MongoDB session for transactions
     * @returns Number of modified documents
     */
    async bulkUpdatePackageStatus(packageIds, isActive, session) {
        const result = await package_model_1.Package.updateMany({ _id: { $in: packageIds } }, { $set: { isActive } }, { session });
        logger_1.default.info('Bulk package status update completed', {
            packageIds,
            isActive,
            modifiedCount: result.modifiedCount,
            timestamp: new Date().toISOString(),
        });
        return result.modifiedCount;
    }
    /**
     * Soft delete a package
     * @throws {NotFoundError} If package not found
     * @throws {BadRequestError} If package has active reservations
     */
    async deletePackage(id, deletedBy) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            // Check if package exists and has no reservations
            const existing = await package_model_1.Package.findOne({
                _id: id,
                isDeleted: false,
            }).session(session);
            if (!existing) {
                throw new app_errors_1.NotFoundError('Package not found', {
                    code: 'PACKAGE_NOT_FOUND',
                });
            }
            if (existing.reserved > 0) {
                throw new app_errors_1.BadRequestError('Cannot delete package with active reservations', {
                    code: 'ACTIVE_RESERVATIONS',
                    details: {
                        reserved: existing.reserved,
                    },
                });
            }
            // Perform soft delete
            const packageDoc = await package_model_1.Package.findByIdAndUpdate(id, {
                isDeleted: true,
                deletedAt: new Date(),
                deletedBy: deletedBy,
                isActive: false, // Mark as inactive when deleted
            }, { new: true, session });
            await session.commitTransaction();
            return packageDoc;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Restore a soft-deleted package
     * @throws {NotFoundError} If package not found or not deleted
     */
    async restorePackage(id) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            const packageDoc = await package_model_1.Package.findOneAndUpdate({
                _id: id,
                isDeleted: true,
            }, {
                isDeleted: false,
                deletedAt: undefined,
                deletedBy: undefined,
                isActive: true, // Restore to active status
            }, { new: true, session });
            if (!packageDoc) {
                throw new app_errors_1.NotFoundError('Deleted package not found', {
                    code: 'DELETED_PACKAGE_NOT_FOUND',
                });
            }
            await session.commitTransaction();
            return packageDoc;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * Permanently delete a package (hard delete)
     * Also deletes associated image file from uploads directory
     * @throws {NotFoundError} If package not found
     */
    async permanentlyDeletePackage(id) {
        const session = await mongoose_1.default.startSession();
        try {
            session.startTransaction();
            const packageDoc = await package_model_1.Package.findOneAndDelete({
                _id: id,
                isDeleted: true, // Only allow permanent deletion of soft-deleted items
            }).session(session);
            if (!packageDoc) {
                throw new app_errors_1.NotFoundError('Deleted package not found', {
                    code: 'DELETED_PACKAGE_NOT_FOUND',
                });
            }
            await session.commitTransaction();
            // After successful database deletion, clean up the image file
            if (packageDoc.imagePath) {
                logger_1.default.info('Cleaning up package image file', {
                    packageId: packageDoc._id,
                    imagePath: packageDoc.imagePath,
                });
                const deleted = await file_cleanup_1.FileCleanupService.deleteImageFile(packageDoc.imagePath);
                if (deleted) {
                    // Optionally clean up empty directories
                    await file_cleanup_1.FileCleanupService.cleanupEmptyDirectories(packageDoc.imagePath);
                }
            }
            return packageDoc;
        }
        catch (error) {
            await session.abortTransaction();
            throw error;
        }
        finally {
            session.endSession();
        }
    }
    /**
     * List soft-deleted packages with pagination
     */
    async listDeletedPackages(pagination = { page: 1, limit: 10 }) {
        const query = { isDeleted: true };
        const [data, total] = await Promise.all([
            package_model_1.Package.find(query)
                .populate('cylinder')
                .populate('includedSpareParts.part')
                .sort({ deletedAt: -1 }) // Most recently deleted first
                .skip((pagination.page - 1) * pagination.limit)
                .limit(pagination.limit),
            package_model_1.Package.countDocuments(query),
        ]);
        return { data, total };
    }
}
exports.packageService = new PackageService();
//# sourceMappingURL=package.services.js.map